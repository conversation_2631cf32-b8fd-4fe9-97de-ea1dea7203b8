// 本地购物车工具类
class LocalCart {
  constructor(bid, tableId = '') {
    this.cartKey = `restaurant_cart_${bid}_${tableId}`;
    this.initCart();
  }
  
  // 初始化购物车
  initCart() {
    const cartData = uni.getStorageSync(this.cartKey);
    if (!cartData) {
      this.saveCart({
        list: [],
        total: 0,
        totalprice: '0.00'
      });
    }
  }
  
  // 获取购物车数据
  getCart() {
    return uni.getStorageSync(this.cartKey) || {
      list: [],
      total: 0,
      totalprice: '0.00'
    };
  }
  
  // 保存购物车数据
  saveCart(cartData) {
    uni.setStorageSync(this.cartKey, cartData);
  }
}