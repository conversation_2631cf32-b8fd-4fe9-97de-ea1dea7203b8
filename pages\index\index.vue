<template>
<view class="container" :style="{backgroundColor:pageinfo.bgcolor}">
	<view class="changebox" >
		<view class="language">
			语言 {{getLange()}}
		</view>
		<view class="change" @tap="tochange">
		Change language
		 <image src="/static/img/changelang.png" class="imgset" />
				
		</view>
    </view>
	<block v-if="platform=='wx' && homeNavigationCustom == 2">
	<view class="navigation" :style="{height:(44+statusBarHeight)+'px','background':navigationBarBackgroundColor}">
		<view :style="{height:statusBarHeight+'px'}"></view>
		<view class='navcontent'>
			<view  class="topinfo">
				<image class="topinfoicon" :src="sysset.logo"/>
				<view class="topinfotxt" :style="{color:navigationBarTextStyle}">{{sysset.name}}</view>
			</view>
			<view class="topsearch" :style="{width:(screenWidth-210)+'px'}" @tap="goto" data-url="/myshop/shop/search">
				<image src="/static/img/search.png"/>
				<text style="font-size:24rpx;color:#999">{{lang('search for items of interest')}}</text>
			</view>
		</view>
	</view>
	<view style="width:100%;" :style="{height:(44+statusBarHeight)+'px'}"></view>
	</block>
	
	<block v-if="sysset.mode == 1">
	<view class="navigation" :style="{height:(44)+'px','background':navigationBarBackgroundColor}">
		<view class='navcontent'>
			<view  class="topinfo">
				<image class="topinfoicon" :src="sysset.logo"/>
				<view class="topinfotxt" :style="{color:navigationBarTextStyle,width:'auto'}">{{sysset.name}}</view>
			</view>
			<view class="topR">
				<text class="btn-text" @tap="goto" data-url="pages/business/clist2">[{{lang('switch')}}]</text><!-- 距离589m， --><block v-if="sysset.address">{{sysset.address}}</block>
			</view>
		</view>
	</view>
	<view style="width:100%;" :style="{height:(44)+'px'}"></view>
	</block>
	
	<block v-if="sysset.showgzts">
		<view style="width:100%;height:88rpx"> </view>
		<view class="follow_topbar">
			<view class="headimg"><image :src="sysset.logo"/></view>
			<view class="info">
				<view class="i">{{lang('welcome to')}} <text :style="{color:t('color1')}">{{sysset.name}}</text></view>
				<view class="i">{{lang('pay attention to the public, more dedicated service')}}</view>
			</view>
			<view class="sub" @tap="showsubqrcode" :style="{'background-color':t('color1')}">{{lang('immediate attention')}}</view>
		</view>
		<uni-popup id="qrcodeDialog" ref="qrcodeDialog" type="dialog">
			<view class="qrcodebox">
				<image :src="sysset.qrcode" @tap="previewImage" :data-url="sysset.qrcode" class="img"/>
				<view class="txt">{{lang('long according to the recognition of qr code')}}</view>
				<view class="close" @tap="closesubqrcode">
					<image src="/static/img/close2.png" style="width:100%;height:100%"/>
				</view>
			</view>
		</uni-popup>
	</block>

	<dp :pagecontent="pagecontent" :menuindex="menuindex"></dp>
	<view class="ggdialog" v-if="guanggaopic && hideguanggao==0">
		<view class="main">
			<!-- <view class="close" @tap="closegg"><image src="/static/img/close.png"></image></view> -->
			<image :src="guanggaopic" class="guanggaopic" @tap="goto" :data-url="guanggaourl" mode="widthFix"></image>
			<view class="close2" @tap="closegg">
				<image src="/static/img/close2.png" style="width:100%;height:100%"/>
			</view>
		</view>
	</view>
	<view class="bobaobox" v-if="oglist.length>0">
		<swiper style="position:relative;height:54rpx;width:450rpx;" autoplay="true" :interval="5000" vertical="true">
			<swiper-item v-for="(item, index) in oglist" :key="index" @tap="goto" :data-url="'/myshop/shop/product?id=' + item.proid" class="flex-y-center">
				<image :src="item.headimg" style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px"></image>
				<view style="width:400rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx">{{item.nickname}} {{item.showtime}}购买了 {{item.name}}</view>
			</swiper-item>
		</swiper>
	</view>
	<view v-if="copyright!=''" class="copyright">{{copyright}}</view>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>
<script>
var app = getApp();
export default {
	data() {
	return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			platform:app.globalData.platform,
			homeNavigationCustom:app.globalData.homeNavigationCustom,
			navigationBarBackgroundColor:app.globalData.navigationBarBackgroundColor,
			navigationBarTextStyle:app.globalData.navigationBarTextStyle,

			id: 0,
			pageinfo: [],
			pagecontent: [],
			hideguanggao: 0,
			sysset: {},
			title: "",
			oglist: [], 
			guanggaopic: "",
			guanggaourl: "",
			copyright:'',
			latitude:'',
			longitude:'',
			statusBarHeight:20,
			screenWidth:375,
			business:[],
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
			console.log('底部tab',app.globalData.initdata)
		var sysinfo = uni.getSystemInfoSync();
		
		this.statusBarHeight = sysinfo.statusBarHeight;
		this.screenWidth = sysinfo.screenWidth;

		this.getdata();
		
	},
	onPullDownRefresh:function(e){
		this.getdata();
	},
	methods: {
		lang: function(k) {
			return app.lang(k);
		},
		getLange(){
			let language=uni.getStorageSync("mylang")
			console.log("language",language)
			if(language=="zh_cn"){
				return '中文'
			}else if(language=="zh_tw"){
				return '中文（繁体）'
			}else if(language=="en"){
				return 'English'
			}else if(language=="vnm"){
				return 'Tiếng Việt'
			}else if(language=="tha"){
				return 'ภาษาไทย'
			}else if(language=="in"){
				return 'भारत'
			}else if(language=="my"){
				return 'Malay'
			}
		},
		changelang(lang){
			console.log(111)
			app.changelangs(lang)
		},
		changelang(lang) {
			console.log(111)
			app.changelangs(lang)
		},
		tochange() {
			app.goto('/pages/index/changelang?type=2', 'reLaunch')
		},
		getdata:function(){
			var that = this;
			var opt = this.opt
			var id = 0;
			if(opt.select_bid){
				var select_bid = opt.select_bid;
				app.setCache('select_bid',select_bid);
			}else{
				var select_bid =app.getCache('select_bid');
			}
			
			if (opt && opt.id) {
			  id = opt.id;
			}
			that.loading = true;
			app.get('ApiIndex/index', {id: id,latitude:that.latitude,longitude:that.longitude,select_bid:select_bid}, function (data) {
				that.loading = false;
			  if (data.status == 2) {
			    //付费查看
			    app.goto('/pages/pay/pay?fromPage=index&id=' + data.payorderid + '&pageid=' + that.id, 'redirect');
			    return;
			  }
			  if (data.status == 1) {
			    var pagecontent = data.pagecontent;
					that.title = data.pageinfo.title;
					that.oglist = data.oglist;
					that.guanggaopic = data.guanggaopic;
					that.guanggaourl = data.guanggaourl;
					that.pageinfo = data.pageinfo;
					that.pagecontent = data.pagecontent;
					that.copyright = data.copyright;
					that.sysset = data.sysset;
					if(data.sysset.mode == 1 && data.business){
						that.business = data.business;
						if(select_bid == '')
						app.setCache('select_bid', data.business.id);
					}
			    uni.setNavigationBarTitle({
			      title: data.pageinfo.title
			    });
					that.loaded();
					if(that.latitude=='' && that.longitude=='' && data.needlocation){
						app.getLocation(function (res) {
							that.latitude = res.latitude;
							that.longitude = res.longitude;
							that.getdata();
						});
					}
			  } else {
			    if (data.msg) {
			      app.alert(data.msg, function () {
			        if (data.url) app.goto(data.url);
			      });
			    } else if (data.url) {
			      app.goto(data.url);
			    } else {
			      app.alert(that.lang('you do not have permission to view'));
			    }
			  }
			});
		},
		closegg: function () {
			this.hideguanggao = 1;
		},
		showsubqrcode:function(){
			this.$refs.qrcodeDialog.open();
		},
		closesubqrcode:function(){
			this.$refs.qrcodeDialog.close();
		},
	}
}
</script>
<style>
.topR{flex: 1;display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 1;
overflow: hidden; color: #666;}
.topR .btn-text {margin: 0 10rpx; color: #333;}

.follow_topbar {height:88rpx; width:100%;max-width:640px; background:rgba(0,0,0,0.8); position:fixed; top:0; z-index:13;}
.follow_topbar .headimg {height:64rpx; width:64rpx; margin:6px; float:left;}
.follow_topbar .headimg image {height:64rpx; width:64rpx;}
.follow_topbar .info {height:56rpx; padding:16rpx 0;}
.follow_topbar .info .i {height:28rpx; line-height:28rpx; color:#ccc; font-size:24rpx;}
.follow_topbar .info {height:80rpx; float:left;}
.follow_topbar .sub {height:48rpx; width:auto; background:#FC4343; padding:0 20rpx; margin:20rpx 16rpx 20rpx 0; float:right; font-size:24rpx; color:#fff; line-height:52rpx; border-radius:6rpx;}
.qrcodebox{background:#fff;padding:50rpx;position:relative;border-radius:20rpx}
.qrcodebox .img{width:400rpx;height:400rpx}
.qrcodebox .txt{color:#666;margin-top:20rpx;font-size:26rpx;text-align:center}
.qrcodebox .close{width:50rpx;height:50rpx;position:absolute;bottom:-100rpx;left:50%;margin-left:-25rpx;border:1px solid rgba(255,255,255,0.6);border-radius:50%;padding:8rpx}

.ggdialog{ position:fixed;z-index:11;width:100%;max-width:640px;height:100%;background:rgba(0,0,0,0.5);display:flex;justify-content:center;align-items:center;top:0;}
.ggdialog .main{ width:80%;height:80%;position:relative;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-top:-40rpx}
.ggdialog .close{ position:absolute;padding:20rpx;top:-80rpx;right:-40rpx}
.ggdialog .close image{ width:40rpx;height:40rpx;}
.ggdialog .guanggaopic{max-width:100%;height:auto;max-height:100%}

.ggdialog .close2{width:50rpx;height:50rpx;border:1px solid rgba(255,255,255,0.5);border-radius:50%;padding:8rpx;margin-top:40rpx}

.bobaobox{position:fixed;top:calc(var(--window-top) + 180rpx);left:20rpx;z-index:10;background:rgba(0,0,0,0.6);border-radius:30rpx;color:#fff;padding:0 10rpx}

	.changebox {
		/* position: absolute; */
		display: flex;
		justify-content: space-between;
		right:30rpx;
		/* top:8rpx; */
		text-align: right;
		padding: 15px;
		color: #8a8a8a;
	}

	.imgset {
		width: 15px;
		height: 15px;
		vertical-align: -.3em;
		margin-left: 0.1em;
	}

.navigation{width:100%;height:64px;background:#fff;position:fixed;z-index:99}
.navcontent{display:flex;align-items:center;height:44px;padding-left:10px}
.navcontent .topinfo{display:flex;align-items:center;}
.navcontent .topinfoicon{width:17px;height:17px;border-radius:4px}
.navcontent .topinfotxt{margin-left:6px;font-size:14px;font-weight:600;width:70px;text-overflow: ellipsis;white-space:nowrap;overflow: hidden;}
.navcontent .topsearch{width:150px;height:32px;background:#f2f2f2;border-radius:16px;color:#232323;display:flex;align-items:center;justify-content:center;font-size:14px}
.navcontent .topsearch image{width:14px;height:15px;margin-right:6px}
.changeclass{
	padding-top: 10px;
	/* float: left; */
	overflow:hidden;
	background: #2188ff;
	width: 100%;
	z-index: 10;
}
.rightclot{
	z-index: 11;
	float: right;
	margin-right: 15px;
	font-size: 26rpx;
	line-height: 40rpx;
	color: darkgreen;
	border: 1px solid darkgreen;
	width: 45px;
	display:inline-block;
	overflow:hidden;
	text-align: center;
}
</style>