<template>
	<view class="container">
		<block v-if="isload">
			<view class="head-bg">
				<h1 class="text-center">{{detail.name}}</h1>
			</view>
			<form @submit="subform">
			<view class="card-view" v-if="detail.status == 2">
				<view class="card-wrap">
					<view class="card-title">餐桌信息</view>
					<view class="info-item">
						<view class="t1">桌台</view>
						<view class="t2">{{detail.name}}</view>
					</view>
					<view class="info-item">
						<view class="t1">人数/座位数</view>
						<view class="t2">{{order.renshu}}/{{detail.seat}}</view>
					</view>
				</view>
				<view class="card-wrap card-goods" v-if="orderGoods.length > 0">
					<view class="card-title">已点菜品({{orderGoodsSum}})</view>
					<view class="info-item" v-for="(item,index) in orderGoods" :key="index">
						<view class="t1">{{item.name}}[{{item.ggname}}]</view>
						<view class="t2">x{{item.num}}</view>
						<view class="t2">{{item.real_totalprice}}</view>
					</view>                    
                    <view class="info-item">
                    	<view class="t1">合计</view>
                    	<view class="t2">x{{orderGoodsSum}}</view>
                    	<view class="t2">{{order.totalprice}}</view>
                    </view>
                    <view class="info-item">
                    	<view class="t1">服务费</view>
                    	<view class="t2">{{order.server_fee}}</view>
                    </view>
                    <view class="info-item">
                    	<view class="t1">茶位费</view>
                    	<view class="t2">{{order.tea_fee}}</view>
                    </view>
					<view class="info-item">
						<view class="t1">税金</view>
						<view class="t2">{{order.taxes}}</view>
					</view>
                    <view class="info-item">
                    	<view class="t1">折扣</view>
                    	<view class="t2" style="text-align: right;"><input type="number" @input="rebate" data-name="rebate3" name="rebate" placeholder="输入折扣"></view>
                    </view>
					<view class="info-item">
						<view class="t1">优惠</view>
						<view class="t2" style="text-align: right;"><input type="number" @input="discount" data-name="discount" name="discount" placeholder="输入优惠金额"></view>
					</view>
					<view class="info-item">
						<view class="t1">实付</view>
						<view class="t2">{{real_totalprice}}</view>
					</view>
				</view>
			</view>
			<view class="btn-view button-sp-area payment">
				<!-- <button type="default" class="btn-default">取消</button>
				<button type="primary" form-type="submit" v-if="detail.status == 2">确认用户已支付</button> -->
                <text>确认用户已支付</text>
                <view class="payment_list">
                    <button form-type="submit"
                        class="payment_item"
                        @tap="payType = item.value"
                        :class="payType == item.value? 'active': ''"
                        v-for="(item, index) in payTypeList" :key="index">{{item.lable}}</button>
                </view>
			</view>
			</form>
				
			<view class="btn-view button-sp-area mb">
				<button type="default" class="btn-default" @tap="goto" data-url="tableWaiter">返回餐桌列表</button>
			</view>
			
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
	</view>
</template>

<script>
var app = getApp();
	export default {
		data() {
			return {
				opt:{},
				loading:false,
				isload: false,
				
				detail:{},
				order:{},
				orderGoods:[],
				business:{},
				nindex:0,
				orderGoodsSum:0,
                //未打折应付
                originalTotalPrice: 0,
                //打折后应付
				real_totalprice:0,
				numArr:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],
                payType: '',
                payTypeList: [
                    {
                      lable: "现金",
                      value: "3"
                    },
                    {
                      lable: "余额",
                      value: "4"
                    },
                    {lable: '本店储值', value: "a"},    
                    {
                      lable: "微信",
                      value: "1"
                    },
                    {
                      lable: "支付宝",
                      value: "2"
                    },
                    {lable: 'QR', value: "b"},
                ],
                discountVal: 0,
                rebateVal: 10
			}
		},
		onLoad: function (opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		onPullDownRefresh: function () {
			this.getdata();
		},
		methods: {
			getdata: function () {
				var that = this;
				that.loading = true;
				app.get('ApiAdminRestaurantTable/detail', {id:that.opt.id}, function (res) {
					that.loading = false;
					if(res.status == 0){
						app.alert(res.msg,function(){
							app.goback();
						});return;
					}
					that.detail = res.info;
					that.order = res.order;
					that.orderGoods = res.order_goods;
					that.orderGoodsSum = res.order_goods_sum;
					that.originalTotalPrice = (Number(res.order.totalprice) 
                                + Number(res.order.tea_fee)
                                + Number(res.order.server_fee) 
                                + Number(res.order.taxes)).toFixed(2);
                                
                    that.real_totalprice = that.originalTotalPrice;
					
					that.loaded();
					//
				});
			},
			subform: function (e) {
				var that = this;
				var info = e.detail.value;
				info.tableId = that.opt.id;
				app.showLoading('提交中');
				app.confirm('请确认用户已完成支付', function(){
                    info.paytype = that.payTypeList.find(p => p.value == that.payType).lable;
					app.post("ApiAdminRestaurantShopOrder/pay", {info: info}, function (res) {
						app.showLoading(false);
						if(res.status == 0) {
							app.alert(res.msg);
						}
						if(res.status == 1) {
							app.alert(res.msg,function(){
								app.goback();
							});
						}
					});
				}, () => {
                    app.showLoading(false);
                })
			},			
			discount: function (e) {
                var discount = e.detail.value;
				if(discount < 0) {
					app.error('请输入正确的金额');return;
				}
				if(discount > this.real_totalprice) {
					app.error('优惠金额不能大于订单金额');return;
				}
				if(discount == '') {
					discount = 0;
				}
				this.discountVal = discount;
                this.realTotalPrice();
			},
            rebate: function (e) {                
                var rebate = e.detail.value;
                if(rebate < 0) {
                	app.error('请输入正确的金额');return;
                }
                if(rebate > 10) {
                	app.error('折扣值不能大于10');return;
                }
                if(rebate == '') {
                	rebate = 10;
                }
                this.rebateVal = rebate;
                this.realTotalPrice();
            },
            realTotalPrice() {
                this.real_totalprice = (this.originalTotalPrice * Number(this.rebateVal) / 10).toFixed(2) 
                            - this.discountVal;
            }
		}
	}
</script>

<style>
    .payment {
        padding: 15px 20px;
        display: flex;
        flex-direction: column;
    }
    .payment_list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        column-gap: 10rpx;
        row-gap: 15rpx;
        margin-top: 20rpx;
    }
    .payment_list uni-button.payment_item {
        width: 32%;
        border: 1rpx solid #FFCF34;
        color: #ffc42e;
        aspect-ratio: 5/3;
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff9ea;
    }
    .payment_list .payment_item.active {
        background: linear-gradient(-90deg, #F7D156 0%, #F9D873 100%);
        color: #000;
    }
	.mb {margin-bottom: 10rpx;}
	.text-center {text-align: center;}
	.container {padding-bottom: 20rpx;}
	.head-bg {width: 100%;height: 320rpx; background: linear-gradient(-90deg, #FFCF34 0%, #FFD75F 100%); color: #333;}
	.head-bg h1 { line-height: 100rpx; font-size: 42rpx;}
	.head-bg .title { align-items: center; width: 94%; margin: 0 auto;}
	.head-bg .image{ width:80rpx;height:80rpx; margin: 0 10rpx;}
	
	.card-wrap { background-color: #FFFFFF; border-radius: 10rpx;padding: 30rpx; margin: 30rpx auto 0; width: 94%;}
	.card-view{ margin-top: -140rpx; }
	.card-wrap .card-title {font-size: 34rpx; color: #333; font-weight: bold;}

	
.info-item{ display:flex;align-items:center;width: 100%; background: #fff; /* border-bottom: 1px #f3f3f3 solid; */height:70rpx;line-height:70rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;line-height:70rpx;line-height:70rpx}
form .info-item .t1 {color: #333; font-size: 30rpx;}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden; padding-right: 10rpx;}
.info-item .t3{ }
.card-goods .t1 {width: 70%;}
.card-goods .t2 {width: 8%; padding-right: 2%;}
.card-goods .t3 {width: 20%;}
.info-textarea { height: auto; line-height: 40rpx;}
.info-textarea textarea {height: 80rpx;}
.info-textarea .t2{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp: unset;overflow: scroll;}

.btn-view { display: flex;justify-content: space-between; margin: 30rpx 0;}
.btn-view button{ width: 90%; border-radius: 10rpx;background: linear-gradient(-90deg, #F7D156 0%, #F9D873 100%); color: #333; font-weight: bold;}
.btn-default {background-color: #FFFFFF;}

.content{ display:flex;width:100%;padding:0 0 10rpx 0;align-items:center;font-size:24rpx}
.content .item{padding:10rpx 0;flex:1;display:flex;flex-direction:column;align-items:center;position:relative}
.content .item .image{ width:80rpx;height:80rpx}
.content .item .iconfont{font-size:60rpx}
.content .item .t3{ padding-top:3px}
.content .item .t2{display:flex;align-items:center;justify-content:center;background: red;color: #fff;border-radius:50%;padding: 0 10rpx;position: absolute;top: 0px;right:20rpx;width:35rpx;height:35rpx;text-align:center;}


</style>
