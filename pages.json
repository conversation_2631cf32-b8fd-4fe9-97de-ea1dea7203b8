{
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarBackgroundColor": "#F8F8F8",
		// "navigationBarTitleText": "",
		"h5": {
			"titleNView": false
		},
		"app-plus": {
			"scrollIndicator": "none"
		}
	},

	"pages": [{
			"path": "pages/index/index",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/index/main",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/index/webView",
			"style": {
				"titleNView": false
			}
		},
		{
			"path": "pages/index/webView2",
			"style": {
				"titleNView": false
			}
		},
		{
			"path": "pages/index/changelang",
			"style": {
				"titleNView": false
			}
		},
		{
			"path": "pages/index/reg",
			"style": {
				// "navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/login",
			"style": {
				// "navigationBarTitleText": "login",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/getpwd",
			"style": {
				// "navigationBarTitleText": "",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/index/bind",
			"style": {
				// "navigationBarTitleText": "bind administrator",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/address/address",
			"style": {
				// "navigationBarTitleText": "Shipping address",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/address/addressadd",
			"style": {
				// "navigationBarTitleText": "Edit Address",
				"enablePullDownRefresh": false
			}
		},

		{
			"path": "pages/article/artlist",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/article/detail",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/article/pinglun",
			"style": {
				// "navigationBarTitleText": "",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/business/index",
			"style": {
				// "navigationBarTitleText": "business details",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/business/main",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/business/clist",
			"style": {
				// "navigationBarTitleText": "business list",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/business/blist",
			"style": {
				// "navigationBarTitleText": "nearby businesses",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/business/apply",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/business/commentlist",
			"style": {
				// "navigationBarTitleText": "business evaluation",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/business/clist2",
			"style": {
				// "navigationBarTitleText": "select business",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/coupon/couponlist",
			"style": {
				// "navigationBarTitleText": "Coupon Collection Center",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/coupon/mycoupon",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/coupon/coupondetail",
			"style": {
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/form/formlog",
			"style": {
				// "navigationBarTitleText": "submit record",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/form/formdetail",
			"style": {
				// "navigationBarTitleText": "details",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/form/formpay",
			"style": {
				// "navigationBarTitleText": "order payment",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/lipin/index",
			"style": {
				// "navigationBarTitleText": "exchange center",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/lipin/prodh",
			"style": {
				// "navigationBarTitleText": "exchange goods",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/lipin/dhlog",
			"style": {
				// "navigationBarTitleText": "exchange record",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/maidan/maidanlog",
			"style": {
				// "navigationBarTitleText": "Purchase order payment record",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/maidan/maidandetail",
			"style": {
				// "navigationBarTitleText": "Bill payment details",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/maidan/pay",
			"style": {
				// "navigationBarTitleText": "pay bill",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/money/moneylog",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/money/withdraw",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/money/recharge",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/money/rechargeToMember",
			"style": {
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/yuebao/yuebaolog",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/yuebao/withdraw",
			"style": {
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/sign/index",
			"style": {
				// "navigationBarTitleText": "check in",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/sign/signrecord",
			"style": {
				// "navigationBarTitleText": "Check-in record",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/kefu/index",
			"style": {
				// "navigationBarTitleText": "online consultation",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/pay/pay",
			"style": {
				// "navigationBarTitleText": "Cashier",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/pay/transfer",
			"style": {
				// "navigationBarTitleText": "transfer money",
				"enablePullDownRefresh": true
			}
		}, {
			"path": "pages/index/changemoney",
			"style": {
				// "navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/shop/search",
			"style": {
				"navigationBarTitleText": "商品搜索",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/logistics/index",
			"style": {
				// "navigationBarTitleText": "",
				// "enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/logistics/order",
			"style": {
				// "navigationBarTitleText": "",
				// "enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/logistics/record",
			"style": {
				// "navigationBarTitleText": "",
				// "enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/logistics/carriage",
			"style": {
				// "navigationBarTitleText": "",
				// "enablePullDownRefresh": false
			}
		},{
			"path": "pages/order/index",
			"style": {
				"navigationBarTitleText": "订单管理",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/order/orderdetail",
			"style": {
				"navigationBarTitleText": "订单详情",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/order/warehouse",
			"style": {
				"navigationBarTitleText": "选择自提仓库",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/forecast/forecast",
			"style": {
				"navigationBarTitleText": "运费预估",
				"enablePullDownRefresh": false
			}
		},
		{
			"path" : "pages/google/googlemap",
			"style" : 
			{
				"navigationBarTitleText" : "谷歌地区",
				"enablePullDownRefresh" : false
			}
		}
	],
	"subPackages": [{
			"root": "activity",
			"pages": [

				{
					"path": "scoreshop/index",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/prolist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/product",
					"style": {
						// "navigationBarTitleText": "product details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/cart",
					"style": {
						// "navigationBarTitleText": "shopping cart",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "scoreshop/orderlist",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "scoreshop/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "kanjia/index",
					"style": {
						// "navigationBarTitleText": "Bargain list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/product",
					"style": {
						// "navigationBarTitleText": "product details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/join",
					"style": {
						// "navigationBarTitleText": "Bargain details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/helplist",
					"style": {
						// "navigationBarTitleText": "help list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kanjia/orderlist",
					"style": {
						// "navigationBarTitleText": "Bargain Order List",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kanjia/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "collage/index",
					"style": {
						// "navigationBarTitleText": "Group product list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/product",
					"style": {
						// "navigationBarTitleText": "product details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/team",
					"style": {
						// "navigationBarTitleText": "Members",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collage/orderlist",
					"style": {
						// "navigationBarTitleText": "Group order list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "collage/comment",
					"style": {
						// "navigationBarTitleText": "I want to comment",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collage/commentlist",
					"style": {
						// "navigationBarTitleText": "product review",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "collage/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "seckill/index",
					"style": {
						// "navigationBarTitleText": "Spike list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/product",
					"style": {
						// "navigationBarTitleText": "Spike products",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "seckill/orderlist",
					"style": {
						// "navigationBarTitleText": "Spike order list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "seckill/comment",
					"style": {
						// "navigationBarTitleText": "I want to comment",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "seckill/commentlist",
					"style": {
						// "navigationBarTitleText": "product review",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "seckill/refund",
					"style": {
						// "navigationBarTitleText": "Request refund",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "tuangou/prolist",
					"style": {
						// "navigationBarTitleText": "Group buy list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/product",
					"style": {
						// "navigationBarTitleText": "Group buy products",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuangou/orderlist",
					"style": {
						// "navigationBarTitleText": "Group purchase order list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "tuangou/comment",
					"style": {
						// "navigationBarTitleText": "I want to comment",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuangou/commentlist",
					"style": {
						// "navigationBarTitleText": "product review",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "tuangou/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "xydzp/index",
					"style": {
						// "navigationBarTitleText": "Wheel of fortune",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "xydzp/myprize",
					"style": {
						// "navigationBarTitleText": "my prize",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "ggk/index",
					"style": {
						// "navigationBarTitleText": "scratch card",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "ggk/myprize",
					"style": {
						// "navigationBarTitleText": "my prize",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "luntan/index",
					"style": {
						// "navigationBarTitleText": "",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/ltlist",
					"style": {
						// "navigationBarTitleText": "",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/detail",
					"style": {
						// "navigationBarTitleText": "dynamic details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/fatie",
					"style": {
						// "navigationBarTitleText": "post",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luntan/pinglun",
					"style": {
						// "navigationBarTitleText": "Comment",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "peisong/dating",
					"style": {
						// "navigationBarTitleText": "Order hall",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/orderlist",
					"style": {
						// "navigationBarTitleText": "my delivery order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/my",
					"style": {
						// "navigationBarTitleText": "personal center",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "peisong/moneylog",
					"style": {
						// "navigationBarTitleText": "balance details"
					}
				},
				{
					"path": "peisong/withdraw",
					"style": {
						// "navigationBarTitleText": "balance withdrawal"
					}
				},
				{
					"path": "peisong/setinfo",
					"style": {
						// "navigationBarTitleText": "Withdrawal Settings"
					}
				},
				{
					"path": "yuyue/selectpeople",
					"style": {
						// "navigationBarTitleText": "personnel list",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/product",
					"style": {
						// "navigationBarTitleText": "Service Details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "yuyue/product2",
					"style": {
						// "navigationBarTitleText": "product details",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/prolist",
					"style": {
						// "navigationBarTitleText": "service list",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/buy2",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/orderlist",
					"style": {
						// "navigationBarTitleText": "Order List",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/logistics",
					"style": {
						// "navigationBarTitleText": "check progress",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/comment",
					"style": {
						// "navigationBarTitleText": "evaluate",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/commentps",
					"style": {
						// "navigationBarTitleText": "evaluate",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/commentlist",
					"style": {
						// "navigationBarTitleText": "evaluation list",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/peolist",
					"style": {
						// "navigationBarTitleText": "personnel list",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/peolist2",
					"style": {
						// "navigationBarTitleText": "master list",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/peodetail",
					"style": {
						// "navigationBarTitleText": "Personnel Details",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/peodetail2",
					"style": {
						// "navigationBarTitleText": "Master Details",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/jdorderlist",
					"style": {
						// "navigationBarTitleText": "Order List",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/jdorderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/my",
					"style": {
						// "navigationBarTitleText": "My",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/dating",
					"style": {
						// "navigationBarTitleText": "My",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/moneylog",
					"style": {
						// "navigationBarTitleText": "Billing Details",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/search",
					"style": {
						// "navigationBarTitleText": "search",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/setinfo",
					"style": {
						// "navigationBarTitleText": "Withdrawal Settings",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/withdraw",
					"style": {
						// "navigationBarTitleText": "My purse",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/login",
					"style": {
						// "navigationBarTitleText": "Agent login",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "yuyue/setpwd",
					"style": {
						// "navigationBarTitleText": "change Password",
						"enablePullDownRefresh": false
					}
				},

				{
					"path": "shortvideo/index",
					"style": {
						// "navigationBarTitleText": "short video list"
					}
				},
				{
					"path": "shortvideo/detail",
					"style": {
						// "navigationBarTitleText": "",
						"navigationStyle": "custom",
						"titleNView": false
					}
				},
				{
					"path": "shortvideo/uploadvideo",
					"style": {
						// "navigationBarTitleText": "Post a short video"
					}
				},
				{
					"path": "shortvideo/myupload",
					"style": {
						// "navigationBarTitleText": "my publication history"
					}
				},


				{
					"path": "kecheng/list",
					"style": {
						// "navigationBarTitleText": "curriculum schedule",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/product",
					"style": {
						// "navigationBarTitleText": "Course Details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "kecheng/mldetail",
					"style": {
						// "navigationBarTitleText": "Catalog Details",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/orderlist",
					"style": {
						// "navigationBarTitleText": "My courses",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/tiku",
					"style": {
						// "navigationBarTitleText": "start answering",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/complete",
					"style": {
						// "navigationBarTitleText": "Answer completed",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/recordlog",
					"style": {
						// "navigationBarTitleText": "answer record",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/error",
					"style": {
						// "navigationBarTitleText": "Review of wrong questions",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "kecheng/category3",
					"style": {
						// "navigationBarTitleText": "sorting classes",
						"enablePullDownRefresh": false
					}
				},

				{
					"path": "luckycollage/classify",
					"style": {
						// "navigationBarTitleText": "Lucky group classification",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/prolist",
					"style": {
						// "navigationBarTitleText": "product list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/product",
					"style": {
						// "navigationBarTitleText": "product details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/team",
					"style": {
						// "navigationBarTitleText": "Members",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luckycollage/orderlist",
					"style": {
						// "navigationBarTitleText": "Group order list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/comment",
					"style": {
						// "navigationBarTitleText": "I want to comment",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luckycollage/commentlist",
					"style": {
						// "navigationBarTitleText": "product review",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "luckycollage/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/index",
					"style": {
						// "navigationBarTitleText": "Open group list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "luckycollage/product2",
					"style": {
						// "navigationBarTitleText": "product details",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "express/index",
					"style": {
						// "navigationBarTitleText": "Express inquiry",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "express/mail",
					"style": {
						// "navigationBarTitleText": "Send Express",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "express/addressadd",
					"style": {
						// "navigationBarTitleText": "add address",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "express/address",
					"style": {
						// "navigationBarTitleText": "address book",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "express/logistics",
					"style": {
						// "navigationBarTitleText": "Logistics information",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "express/kddetail",
					"style": {
						// "navigationBarTitleText": "Express Details",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "toupiao/index",
					"style": {
						// "navigationBarTitleText": "details of the event"
					}
				},
				{
					"path": "toupiao/detail",
					"style": {
						// "navigationBarTitleText": "voting details"
					}
				},
				{
					"path": "toupiao/phb",
					"style": {
						// "navigationBarTitleText": "leaderboard"
					}
				},
				{
					"path": "toupiao/baoming",
					"style": {
						// "navigationBarTitleText": "I want to sign up"
					}
				},
				{
					"path": "toupiao/shuoming",
					"style": {
						// "navigationBarTitleText": "Event Description"
					}
				},

				{
					"path": "hongbaoEveryday/index",
					"style": {
						// "navigationBarTitleText": "Activity",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "hongbaoEveryday/log",
					"style": {
						// "navigationBarTitleText": "get record",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "hongbaoEveryday/eduLog",
					"style": {
						// "navigationBarTitleText": "Quota record",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "hongbaoEveryday/withdraw",
					"style": {
						// "navigationBarTitleText": "withdraw",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "workorder/index",
					"style": {
						// "navigationBarTitleText": "Ticket submission",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/detail",
					"style": {
						// "navigationBarTitleText": "Ticket Details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/record",
					"style": {
						// "navigationBarTitleText": "Ticket list",
						"enablePullDownRefresh": true
					}
				}

				, {
					"path": "yx/kouling",
					"style": {
						// "navigationBarTitleText": "password",
						"enablePullDownRefresh": true
					}
				}, {
					"path": "yx/riddle",
					"style": {
						// "navigationBarTitleText": "Riddles",
						"enablePullDownRefresh": false
					}
				}, {
					"path": "yx/jidian",
					"style": {
						// "navigationBarTitleText": "Activity Rules",
						"enablePullDownRefresh": false
					}
				}


			]
		},
		{
			"root": "admin",
			"pages": [{
					"path": "index/login",
					"style": {
						// "navigationBarTitleText": "Admin login"
					}
				},
				{
					"path": "index/index",
					"style": {
						// "navigationBarTitleText": "control center",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "index/setpwd",
					"style": {
						// "navigationBarTitleText": "change Password"
					}
				},
				{
					"path": "index/setinfo",
					"style": {
						// "navigationBarTitleText": "store settings"
					}
				},
				{
					"path": "index/recharge",
					"style": {
						// "navigationBarTitleText": "store settings"
					}
				},

				{
					"path": "hexiao/hexiao",
					"style": {}
				},
				{
					"path": "hexiao/record",
					"style": {
						// "navigationBarTitleText": "my write-off",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/index",
					"style": {
						// "navigationBarTitleText": "finance"
					}
				},
				{
					"path": "finance/commissionlog",
					"style": {}
				},
				{
					"path": "finance/comwithdrawdetail",
					"style": {}
				},
				{
					"path": "finance/comwithdrawlog",
					"style": {}
				},
				{
					"path": "finance/moneylog",
					"style": {}
				},
				{
					"path": "finance/rechargelog",
					"style": {
						// "navigationBarTitleText": "Recharge record"
					}
				},
				{
					"path": "finance/withdrawdetail",
					"style": {}
				},
				{
					"path": "finance/withdrawlog",
					"style": {}
				},
				{
					"path": "finance/bmoneylog",
					"style": {
						// "navigationBarTitleText": "balance details"
					}
				},
				{
					"path": "finance/bwithdraw",
					"style": {
						// "navigationBarTitleText": "balance withdrawal"
					}
				},
				{
					"path": "finance/bwithdrawlog",
					"style": {
						// "navigationBarTitleText": "Withdrawals record"
					}
				},
				{
					"path": "finance/txset",
					"style": {
						// "navigationBarTitleText": "Withdrawal information"
					}
				},
				{
					"path": "finance/yuebaowithdrawlog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "finance/yuebaowithdrawdetail",
					"style": {}
				},
				{
					"path": "finance/yuebaolog",
					"style": {}
				},

				{
					"path": "kefu/index",
					"style": {
						// "navigationBarTitleText": "message list"
					}
				},
				{
					"path": "kefu/message",
					"style": {}
				},
				{
					"path": "member/index",
					"style": {}
				},
				{
					"path": "member/detail",
					"style": {}
				},
				{
					"path": "order/collageorder",
					"style": {
						// "navigationBarTitleText": "Group order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/collageorderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/luckycollageorder",
					"style": {
						// "navigationBarTitleText": "Lucky group order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/luckycollageorderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/kanjiaorder",
					"style": {
						// "navigationBarTitleText": "Bargain order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/kanjiaorderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/seckillorder",
					"style": {
						// "navigationBarTitleText": "Lightning order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/seckillorderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "order/seckill2order",
					"style": {
						// "navigationBarTitleText": "Lightning order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/seckill2orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},


				{
					"path": "order/yuyueorder",
					"style": {
						// "navigationBarTitleText": "Appointment order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/yuyueorderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},


				{
					"path": "order/scoreshoporder",
					"style": {
						// "navigationBarTitleText": "Points redemption order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/scoreshoporderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/shoporder",
					"style": {
						// "navigationBarTitleText": "Mall order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/shoporderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/shopRefundOrder",
					"style": {
						// "navigationBarTitleText": "refund order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/shopRefundOrderDetail",
					"style": {
						// "navigationBarTitleText": "Refund Order Details",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "order/tuangouorder",
					"style": {
						// "navigationBarTitleText": "Group purchase order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/tuangouorderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "workorder/record",
					"style": {
						// "navigationBarTitleText": "Process work order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/add",
					"style": {
						// "navigationBarTitleText": "Submit a ticket",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/formlog",
					"style": {
						// "navigationBarTitleText": "Work order record",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/formdetail",
					"style": {
						// "navigationBarTitleText": "Ticket Details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "workorder/myformdetail",
					"style": {
						// "navigationBarTitleText": "Merchant work order details",
						"enablePullDownRefresh": true
					}
				},



				{
					"path": "form/formlog",
					"style": {
						// "navigationBarTitleText": "form submission record"
					}
				},
				{
					"path": "form/formdetail",
					"style": {
						// "navigationBarTitleText": "form submission record"
					}
				},

				{
					"path": "shortvideo/uploadvideo",
					"style": {
						// "navigationBarTitleText": "Post a short video"
					}
				},
				{
					"path": "shortvideo/myupload",
					"style": {
						// "navigationBarTitleText": "my publication history"
					}
				},

				{
					"path": "product/edit",
					"style": {
						// "navigationBarTitleText": "Commodity settings"
					}
				},
				{
					"path": "product/index",
					"style": {
						// "navigationBarTitleText": "commodity management",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "index/businessqr",
					"style": {
						// "navigationBarTitleText": "Promotion code"
					}
				}
				// #custom if restaurant
				, {
					"path": "restaurant/product/edit",
					"style": {
						// "navigationBarTitleText": "menu settings"
					}
				},
				{
					"path": "restaurant/product/index",
					"style": {
						// "navigationBarTitleText": "Dishes management",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/category/edit",
					"style": {
						// "navigationBarTitleText": "Dishes category settings"
					}
				},
				{
					"path": "restaurant/category/index",
					"style": {
						// "navigationBarTitleText": "Dishes classification management",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableEdit",
					"style": {
						// "navigationBarTitleText": "table setting"
					}
				},
				{
					"path": "restaurant/table",
					"style": {
						// "navigationBarTitleText": "table management",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableWaiter",
					"style": {
						// "navigationBarTitleText": "table management",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableWaiterDetail",
					"style": {
						// "navigationBarTitleText": "table details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableWaiterPay",
					"style": {
						// "navigationBarTitleText": "settlement",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/tableCategoryEdit",
					"style": {
						// "navigationBarTitleText": "Dining table classification setting"
					}
				},
				{
					"path": "restaurant/tableCategory",
					"style": {
						// "navigationBarTitleText": "Dining table classification setting",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/takeawayorder",
					"style": {
						// "navigationBarTitleText": "takeaway order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/takeawayorderdetail",
					"style": {
						// "navigationBarTitleText": "takeaway order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/shoporder",
					"style": {
						// "navigationBarTitleText": "order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/shoporderdetail",
					"style": {
						// "navigationBarTitleText": "order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/shoporderEdit",
					"style": {
						// "navigationBarTitleText": "order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/bookingorder",
					"style": {
						// "navigationBarTitleText": "pre-order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/bookingorderdetail",
					"style": {
						// "navigationBarTitleText": "pre-order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/depositorder",
					"style": {
						// "navigationBarTitleText": "consignment order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/depositorderdetail",
					"style": {
						// "navigationBarTitleText": "consignment order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/booking",
					"style": {
						// "navigationBarTitleText": "add booking",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/bookingTableList",
					"style": {
						// "navigationBarTitleText": "choose table",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/queue",
					"style": {
						// "navigationBarTitleText": "call in line",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/queueCategory",
					"style": {
						// "navigationBarTitleText": "queue management",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "restaurant/queueCategoryEdit",
					"style": {
						// "navigationBarTitleText": "queue management",
						"enablePullDownRefresh": true
					}
				},
                {
                  "path": "camera/camera",
                  "style": {
                    "enablePullDownRefresh": false
                  }
                }
				// #custom endif
			]
		}
		// #custom if restaurant
		, {
			"root": "restaurant",
			"pages": [{
					"path": "takeaway/blist",
					"style": {
						// "navigationBarTitleText": "business list"
					}
				},
				{
					"path": "takeaway/index",
					"style": {
						// "navigationBarTitleText": "business details"
					}
				},
				{
					"path": "takeaway/product",
					"style": {
						// "navigationBarTitleText": "product details"
					}
				},
				{
					"path": "takeaway/commentlist",
					"style": {
						// "navigationBarTitleText": "product review",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation"
					}
				},
				{
					"path": "takeaway/orderlist",
					"style": {
						// "navigationBarTitleText": "takeaway order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/comment",
					"style": {
						// "navigationBarTitleText": "I want to comment",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/commentdp",
					"style": {
						// "navigationBarTitleText": "store evaluation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/commentps",
					"style": {
						// "navigationBarTitleText": "Evaluate the delivery person",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "takeaway/orderdetail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/logistics",
					"style": {
						// "navigationBarTitleText": "View Logistics",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "takeaway/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "shop/index",
					"style": {
						// "navigationBarTitleText": "order"
					}
				},
				{
					"path": "shop/search",
					"style": {
						// "navigationBarTitleText": "search"
					}
				},
				{
					"path": "shop/product",
					"style": {
						// "navigationBarTitleText": "product details"
					}
				},
				{
					"path": "shop/commentlist",
					"style": {
						// "navigationBarTitleText": "product review",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation"
					}
				},
				{
					"path": "shop/orderlist",
					"style": {
						// "navigationBarTitleText": "order record",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/comment",
					"style": {
						// "navigationBarTitleText": "I want to comment",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/commentdp",
					"style": {
						// "navigationBarTitleText": "store evaluation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/commentps",
					"style": {
						// "navigationBarTitleText": "Evaluate the delivery person",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/orderdetail",
					"style": {
						// "navigationBarTitleText": "订单详情",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/logistics",
					"style": {
						// "navigationBarTitleText": "View Logistics",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "booking/add",
					"style": {
						// "navigationBarTitleText": "Reserve",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "booking/tableList",
					"style": {
						// "navigationBarTitleText": "table list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "booking/orderlist",
					"style": {
						// "navigationBarTitleText": "scheduled list",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "booking/detail",
					"style": {
						// "navigationBarTitleText": "booking details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "queue/index",
					"style": {
						// "navigationBarTitleText": "queue information",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "queue/quhao",
					"style": {
						// "navigationBarTitleText": "queue up",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "queue/record",
					"style": {
						// "navigationBarTitleText": "queue record",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "deposit/orderlist",
					"style": {
						// "navigationBarTitleText": "deposit all"
					}
				},
				{
					"path": "deposit/orderdetail",
					"style": {
						// "navigationBarTitleText": "storage details"
					}
				},
				{
					"path": "deposit/add",
					"style": {
						// "navigationBarTitleText": "deposit"
					}
				}
			]
		},
		{
			"root": "myshop",
			"pages": [{
					"path": "order/orderlist",
					"style": {
						// "navigationBarTitleText": "Order List",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/refundlist",
					"style": {
						// "navigationBarTitleText": "refund order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/refundDetail",
					"style": {
						// "navigationBarTitleText": "Refund Details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/comment",
					"style": {
						// "navigationBarTitleText": "I want to comment",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "order/commentdp",
					"style": {
						// "navigationBarTitleText": "store evaluation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "order/commentps",
					"style": {
						// "navigationBarTitleText": "Evaluate the delivery person",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "order/detail",
					"style": {
						// "navigationBarTitleText": "order details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/logistics",
					"style": {
						// "navigationBarTitleText": "View Logistics",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/refundSelect",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/refund",
					"style": {
						// "navigationBarTitleText": "Request a refund",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order/invoice",
					"style": {
						// "navigationBarTitleText": "invoice",
						"enablePullDownRefresh": true
					}
				},

				{
					"path": "shop/product",
					"style": {
						// "navigationBarTitleText": "product details",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/commentlist",
					"style": {
						// "navigationBarTitleText": "product review",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/cart",
					"style": {
						// "navigationBarTitleText": "shopping cart",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "shop/prolist",
					"style": {
						// "navigationBarTitleText": "product list",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/search",
					"style": {
						// "navigationBarTitleText": "product search",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category1",
					"style": {
						// "navigationBarTitleText": "Categories",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category2",
					"style": {
						// "navigationBarTitleText": "Categories",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category3",
					"style": {
						// "navigationBarTitleText": "Categories",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/category4",
					"style": {
						// "navigationBarTitleText": "Categories",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/classify",
					"style": {
						// "navigationBarTitleText": "Categories",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/classify2",
					"style": {
						// "navigationBarTitleText": "Categories",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/fastbuy",
					"style": {
						// "navigationBarTitleText": "quick buy",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/fastbuy2",
					"style": {
						// "navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/buy",
					"style": {
						// "navigationBarTitleText": "Order Confirmation",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "shop/mendian",
					"style": {
						// "navigationBarTitleText": "shop",
						"enablePullDownRefresh": false
					}
				},

				{
					"path": "my/usercenter",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/levelinfo",
					"style": {
						// "navigationBarTitleText": "Grade Description",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/levelup",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/leveluppay",
					"style": {
						// "navigationBarTitleText": "订单支付",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/scorelog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/scoreTransfer",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/scoreWithdraw",
					"style": {
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/favorite",
					"style": {
						// "navigationBarTitleText": "my collection",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/history",
					"style": {
						// "navigationBarTitleText": "my footprint",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/paypwd",
					"style": {
						// "navigationBarTitleText": "Set up payment password",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/set",
					"style": {
						// "navigationBarTitleText": "Personal settings",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/setpwd",
					"style": {
						// "navigationBarTitleText": "Change Password",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "my/setnickname",
					"style": {
						// "navigationBarTitleText": "Edit Nikename",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setrealname",
					"style": {
						// "navigationBarTitleText": "Edit Name",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/settel",
					"style": {
						// "navigationBarTitleText": "Eidt mobile phone",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setsex",
					"style": {
						// "navigationBarTitleText": "Edit Gender",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setbirthday",
					"style": {
						// "navigationBarTitleText": "set birthday",
						"enablePullDownRefresh": false
					}
				},
				//{"path": "my/setweixin","style": {"navigationBarTitleText": "Set WeChat ID","enablePullDownRefresh": false}},
				{
					"path": "my/setaliaccount",
					"style": {
						// "navigationBarTitleText": "Set up Alipay account",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "my/setbankinfo",
					"style": {
						// "navigationBarTitleText": "Set Bank Info",
						"enablePullDownRefresh": false
					}
				},

				{
					"path": "commission/index",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionlog",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/commissionlogMendian",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/orderMendian",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/withdraw",
					"style": {
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myteam",
					"style": {
						// "navigationBarTitleText": "My teams",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/downorder",
					"style": {
						// "navigationBarTitleText": "My Orders",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/poster",
					"style": {
						// "navigationBarTitleText": "share poster",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/fhlog",
					"style": {
						// "navigationBarTitleText": "dividend record",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/fhorder",
					"style": {
						// "navigationBarTitleText": "dividend order",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/fenhong",
					"style": {
						// "navigationBarTitleText": "Shareholder dividends",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/teamfenhong",
					"style": {
						// "navigationBarTitleText": "Team bonus",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/areafenhong",
					"style": {
						// "navigationBarTitleText": "Regional agency dividends",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/orderYeji",
					"style": {
						// "navigationBarTitleText": "performance statistics",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/myteamline",
					"style": {
						// "navigationBarTitleText": "my subordinates",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "commission/mysameline",
					"style": {
						// "navigationBarTitleText": "Members of the same level",
						"enablePullDownRefresh": true
					}
				}
			]

		}
		// #custom endif
	],
	"navigateToMiniProgramAppIdList": [
		"wx74316936e2553915",
		"wx2b03c6e691cd7370"
	],
	"sitemapLocation": "sitemap.json",
	"permission": {
		"scope.userLocation": {
			"desc": "Your location information will be used to obtain distance information"
		}
	},
	"condition": { //mode configuration，Valid only during development
		"current": 0, //currently active mode(index item of list)
		"list": [{
			"name": "", //schema name
			"path": "pages/index/index", //start page, required
			"query": "" //Start parameters, obtained in the onLoad function of the page
		}]
	}
}
