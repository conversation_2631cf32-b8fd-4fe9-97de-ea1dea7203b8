<template>
	<view class="container">
		<view class="ordertop" :style="'background:url(' + pre_url + '/static/img/ordertop.png);background-size:100%'">
		</view>
		<view class="address">
			<view class="address-item">
				<view class="address-item-t">
					<text>自提点:</text>
					<text>{{datainfo.detail.freight_text}}</text>
				</view>
				<view class="">
					{{datainfo.storeinfo.address}}{{datainfo.storeinfo.name}}
				</view>
				<view class="address-item-t">
					<text>{{}}名字</text>
					<text>{{datainfo.detail.tel}}</text>
				</view>
			</view>
			<view class="address-item address-item-border">
				<view class="address-item-t">
					<text>收货地址:</text>
				</view>
				<view class="">
					{{addressInfo.address}}
				</view>
				<view class="address-item-t">
					<text>{{addressInfo.name}}</text>
					<text>{{addressInfo.tel}}</text>
				</view>
			</view>
		</view>
		<view class="orderc">
			<view class="orderc-item">
				<text>订单编号</text>
				<text>{{datainfo.detail.ordernum}}</text>
			</view>
			<view class="orderc-item">
				<text>下单时间</text>
				<text>{{datainfo.detail.createtime}}</text>
			</view>
			<view class="orderc-item">
				<text>配送方式</text>
				<text>{{datainfo.detail.freight_text}}</text>
			</view>
			<view class="orderc-item">
				<text>物品属性</text>
				<text>{{}}</text>
			</view>
			<view class="orderc-item">
				<text>称重重量</text>
				<text>{{datainfo.detail.weight}}</text>
			</view>
			<view class="orderc-item">
				<text>体积（长*宽*高）</text>
				<text>{{datainfo.detail.length}}*{{datainfo.detail.width}}*{{datainfo.detail.height}}</text>
			</view>
			<view class="orderc-item">
				<text>总金额</text>
				<text>{{datainfo.detail.totalprice}}</text>
			</view>
			<view class="orderc-item">
				<text>订单付款状态</text>

				<block>
					<text v-if="datainfo.detail.status == 0">未付款</text>
					<text v-else>已付款</text>
				</block>
				  
			</view>
			<view class="">
				<text>打包照片</text>
				<view class="img">
					<img :src="datainfo.detail.ocrimage" alt="" @click="previewImage">
				</view>
				  
			</view>
			<view class="orderc-item address-item-border">
				<text>备注</text>
				<text>{{}}</text>
			</view>
		</view>

		<view class="orderm">
			<view class="orderm-item">
				<text>快递状态</text>
			</view>
			<view class="orderm-item">
				<text>地点</text>
				<text>某某某</text>
				<text>时间</text>
			</view>
		</view>
		<view class="order-button">
			<view class=""></view>
			<view class="order-b">

				  
				<view class="btn2" >物流跟踪</view>
				<view  class="btn1"
                  :style="{background:t('color1')}" @tap="hexiao" :data-id="datainfo.detail.ordernum">确认收货</view>
				<view v-if="datainfo.detail.status == 0" class="btn2" >去付款</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				datainfo: {},
				oderid: "",
				pre_url: app.globalData.pre_url,
				addressInfo:{},
				picUrl: []
			}
		},
		onLoad(e) {
			this.oderid = e.id;
			this.getdata();

			console.log(this.datainfo.detail.ordernum);
			
		},
		methods: {
			getdata() {
				var that = this;
				app.get('ApiOrder/detail', {
					id: that.oderid
				}, function(res) {
					// that.datainfo=res;
					console.log(res)
					that.datainfo = res;
					that.picUrl[0] = res.detail.ocrimage


				})
				app.get('ApiAddress/address', {
				}, function(res) {
					for(var i=0;i<res.data.length;i++){
						if(res.data[i].isdefault==1){
							that.addressInfo=res.data[i]
							
						}
					}
				});
			},
			previewImage() {
				console.log('click');
				
				uni.previewImage({
					urls: this.picUrl,
				})
			},

			hexiao:function(e){
				let that = this;
				let orderid = e.currentTarget.dataset.id
				console.log(orderid);
				
				app.confirm('确定要核销并改为已完成状态吗?', function () {
					app.showLoading('提交中');
					app.post('ApiAdminOrder/hexiao', { type:'shop',orderid: orderid }, function (data) {
						app.showLoading(false);
						app.success(data.msg);
						setTimeout(function () {
							that.getdata();
						}, 1000)
					})
				});
			},
		}
	}
</script>

<style>
	.text-min {
		font-size: 24rpx;
		color: #999;
	}

	.ordertop {
		width: 100%;
		height: 220rpx;
		padding: 50rpx 0 0 70rpx;
	}

	.address-item {
		width: 90%;
		margin: 30rpx auto;
		line-height: 50rpx;
	}

	.orderc {
		width: 90%;
		margin: 30rpx auto;
		line-height: 80rpx;
	}

	.orderm {
		width: 90%;
		margin: 30rpx auto;
		line-height: 80rpx;
	}

	.orderm-item {
		display: flex;
		justify-content: space-between;
	}

	.orderc-item {
		display: flex;
		justify-content: space-between;
	}

	.address-item-t {
		display: flex;
		justify-content: space-between;
	}

	.address-item-border {
		padding-bottom: 20rpx;
		border-bottom: 1px solid #cbcbcb;
	}
	.order-button{
		width: 90%;
		margin: 0 auto;
		padding-top: 50rpx;
		padding-bottom: 100rpx;
		display: flex;justify-content: space-between;
	}
	.order-b{
		display: flex;justify-content: space-between;
	}


	img {
		width: 100rpx;
		height: 200rpx;
	}

	.btn1 {
		margin-left: 20rpx;
		margin-top: 10rpx;
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		color: #fff;
		border-radius: 3px;
		text-align: center;
	}

	.btn2 {
		margin-left: 20rpx;
		margin-top: 10rpx;
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		color: #333;
		background: #fff;
		border: 1px solid #cdcdcd;
		border-radius: 3px;
		text-align: center;
	}

</style>
